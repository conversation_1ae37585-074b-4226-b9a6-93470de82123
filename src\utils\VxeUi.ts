import VxeUI from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import CopyBtn from '@/components/CopyBtn/index.vue'
import BaseBadge from '@/components/BaseBadge/index.vue'
import { BadgeType } from '@/components/BaseBadge/type'
import { h, withDirectives } from 'vue'
import { vCopy } from '@/directive/copy'
import { number2 } from '.'

VxeUI.formats.add('number', {
  tableCellFormatMethod: ({ cellValue }) => number2(cellValue, 2),
  tableFooterCellFormatMethod: ({ itemValue }) => number2(itemValue, 2),
})

/** 用于在单元格复制按钮操作 */
VxeUI.renderer.add('copy', {
  renderTableDefault: (_renderOpts, renderParams) => {
    const value = renderParams.row[renderParams.column.field]
    return h('div', {}, [h('span', {}, value || '--'), value && withDirectives(h(CopyBtn), [[vCopy, String(value)]])])
  },
})
VxeUI.renderer.add('badge', {
  renderTableDefault: (_renderOpts, renderParams) => {
    const value = renderParams.row[renderParams.column.field]
    if (!value) return h('span', {}, '--')
    let type: BadgeType = 'default'
    if (/拒绝|超时|未通过|不通过|失败/.test(value)) type = 'error'
    else if (/未|待/.test(value)) type = 'warning'
    else if (/默认|中|进行/.test(value)) type = 'info'
    else if (/通过|成功|完成|确认|完全|正常/.test(value)) type = 'success'
    else if (/作废|取消|关闭/.test(value)) type = 'default'
    return h(BaseBadge, { label: value, type })
  },
})
export default VxeUI
