import { PageType } from './enum'

// 通用的空值处理formatter函数
const defaultFormatter = ({ cellValue }: { cellValue: any }) => {
  if (cellValue === null || cellValue === undefined || cellValue === '') {
    return '--'
  }
  return cellValue
}

// 金额格式化函数，添加￥符号
const priceFormatter = ({ cellValue }: { cellValue: any }) => {
  if (cellValue === null || cellValue === undefined || cellValue === '') {
    return '--'
  }
  const numValue = Number(cellValue)
  if (Number.isNaN(numValue)) {
    return '--'
  }
  return `￥${numValue.toFixed(2)}`
}

export const pageTableConfig = {
  [PageType.ROLE_MANAGE]: [
    { key: 'seq', name: '序号', width: 60, freeze: 0, is_show: true, is_sort: false },
    { key: 'role_name', name: '角色名称', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'creater', name: '创建人', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'create_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'modifier', name: '更新人', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'update_at', name: '更新时间', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.ATTR_MANAGEMENT]: [
    { key: 'code', name: '属性编码', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'attr_name_language_1', name: '属性名称', freeze: 0, is_show: true, is_sort: false },
    { key: 'attr_group_name', name: '属性分组', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'type', name: '数据类型', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'create_at', name: '创建时间', width: 160, freeze: 0, is_show: true, is_sort: true },
    { key: 'update_at', name: '最后修改时间', width: 160, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 120, freeze: 2, is_show: true },
  ],
  [PageType.CategoryTemplate]: [
    { key: 'code', name: '类目模板编码', width: 180, freeze: 0, is_show: true, is_sort: false },
    { key: 'name', name: '类目模板名称', freeze: 0, is_show: true, is_sort: false },
    { key: 'version_number', name: '版本号', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'create_at', name: '创建时间', width: 160, freeze: 0, is_show: true, is_sort: true },
    { key: 'update_at', name: '最后修改时间', width: 160, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 120, freeze: 2, is_show: true },
  ],

  [PageType.BRAND_MANAGEMENT]: [
    { key: 'brand_number', name: '品牌编码', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'brand_name', name: '品牌名称', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'logo_id', name: 'LOGO', width: 80, freeze: 0, is_show: true, is_sort: false },
    { key: 'supplier_number', name: '所属供应商编码', width: 170, freeze: 0, is_show: true, is_sort: false },
    { key: 'supplier_name', name: '所属供应商名称', width: 170, freeze: 0, is_show: true, is_sort: false },
    { key: 'manufacturer_name', name: '默认制造商', width: 100, freeze: 0, is_show: true, is_sort: false },

    { key: 'auth_file_original_name', name: '品牌授权书(中文)', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'auth_file_en_original_name', name: '品牌授权书(英文)', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'auth_period', name: '授权期限', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'auth_status', name: '授权书状态', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'auth_version', name: '授权书版本号', width: 80, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 100, freeze: 0, is_show: true, is_sort: true },

    { key: 'create_at', name: '创建时间', width: 150, freeze: 0, is_show: true, is_sort: true },
    { key: 'modified_at', name: '最后修改时间', width: 150, freeze: 0, is_show: true, is_sort: true },
    { key: 'plm_brand_code', name: 'PLM品牌编码 ', width: 140, freeze: 0, is_show: true, is_sort: false, formatter: priceFormatter },
    { key: 'operate', name: '操作', width: 80, freeze: 2, is_show: true },
  ],
  [PageType.USER_MANAGE]: [
    { key: 'seq', name: '序号', width: 60, freeze: 0, is_show: true, is_sort: false },
    { key: 'uid', name: '账号', freeze: 0, is_show: true, is_sort: true },
    { key: 'job_id', name: '工号', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'real_name', name: '姓名', width: 150, freeze: 0, is_show: true, is_sort: true },
    { key: 'department_name', name: '部门', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'position_name', name: '岗位', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'leader_name', name: '直接上级', width: 100, freeze: 0, is_show: true, is_sort: true },
    // { key: 'customer_name', name: '所属客户', width: 120, freeze: 0, is_show: true, is_sort: true },
    { key: 'role_names', name: '角色', width: 100, freeze: 0, is_show: true, is_sort: true },
    // { key: 'source_type', name: '来源类型', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'status', name: '状态', width: 80, freeze: 0, is_show: true, is_sort: true },
    { key: 'create_at', name: '创建时间', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'update_at', name: '最近修改时间', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 100, freeze: 2, is_show: true },
  ],

  [PageType.NOTICE_MANAGE]: [
    { key: 'no', name: '通知编码', width: 130, freeze: 0, is_show: true, is_must: false, index: 10 },
    { key: 'title', name: '通知标题', width: 100, freeze: 0, is_show: true, index: 20 },
    { key: 'content', name: '通知内容', width: 200, freeze: 0, is_show: true, index: 30 },
    { key: 'scope', name: '通知范围', width: 150, freeze: 0, is_show: true, is_sort: true, index: 40 },
    { key: 'scheduled_publish_at', name: '计划发布时间', width: 160, freeze: 0, is_show: true, is_sort: true, index: 50 },
    { key: 'notice_status', name: '状态', width: 105, freeze: 0, is_show: true, is_sort: true, index: 60 },
    { key: 'publish_at', name: '发布时间', width: 160, freeze: 0, is_show: true, is_sort: true, index: 70 },
    { key: 'create_at', name: '创建时间', width: 150, freeze: 0, is_show: true, is_sort: true, index: 80 },
    { key: 'modified', name: '最后修改时间', width: 150, freeze: 0, is_show: true, is_sort: true, index: 90 },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true, is_must: true, index: 91 },
  ],
  [PageType.PLATFORM_LARGE_CATEGORIES]: [
    { key: 'name', name: '平台大类名称', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'brand_authorization', name: '品牌授权', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'create_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'update_at', name: '最后修改时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.PLATFORM_SUB_CATEGORIES]: [
    { key: 'name', name: '平台小类名称', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'platform_category_id_name', name: '关联大类', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'brand_authorization', name: '品牌授权', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'store_type', name: '店铺类型', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'document_type', name: '注册证件类型', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'registration_limit', name: '可注册次数', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'margin', name: '保证金', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'register_designated_site_name', name: '注册指定站点', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'create_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'update_at', name: '最后修改时间', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.COUNTRY]: [
    { key: 'country_region_code', name: '国家/地区编码', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'country_region_name', name: '国家/地区名称', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'source_type', name: '数据来源', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.PURPLE_BIRD_MAIN_ACCOUNT]: [
    { key: 'enterprise', name: '企业', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'account_number', name: '主账号', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'password', name: '密码', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'create_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'update_at', name: '最后修改时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.PURPLE_BIRD_DEVICE]: [
    { key: 'device_name', name: '设备名称', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'enterprise', name: '企业', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'equipment_cost', name: '设备费用', width: 0, freeze: 0, is_show: true, is_sort: false, formatter: priceFormatter },
    { key: 'shop_math', name: '关联店铺数', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'opening_time', name: '开通时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'expiration_time', name: '到期时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'create_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'update_at', name: '最后修改时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.PURPLE_BIRD_STAFF_ACCOUNT]: [
    { key: 'account_number', name: '员工账号', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'enterprise', name: '企业', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'password', name: '密码', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'principal', name: '负责人', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'create_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'update_at', name: '最后修改时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.MOBILE_NUMBER]: [
    { key: 'phone_number', name: '手机号', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'holder', name: '持有人', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'source_type', name: '数据来源', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.EMAIL_MANAGEMENT]: [
    { key: 'email', name: '邮箱号', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'source_type', name: '数据来源', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.LICENSE_MANAGEMENT]: [
    { key: 'license_name', name: '执照名称', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'license_type', name: '执照类型', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'legal_person', name: '法人', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'affiliated_division', name: '归属分部', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'attachment', name: '附件', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'source_type', name: '数据来源', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.FinanceAccount_GetList]: [
    { key: 'account', name: '账号', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'funding_platform_id_name', name: '资金平台', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'account_name', name: '账号名称', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'legal_person', name: '法人', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'bank_of_account_opening', name: '开户行', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'cardholder', name: '持卡人', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'usage_type_name', name: '用途类型', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'source_type_name', name: '数据来源', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.LegalPerson_GetList]: [
    { key: 'name', name: '法人名称', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'document_type_name', name: '证件类型', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'document_number', name: '证件号码', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'phone_number', name: '手机号码', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'attachment', name: '附件', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'source_type_name', name: '数据来源', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.SHOP_REGIST_FLOW]: [
    { key: 'title', name: '流程标题', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'number', name: '流水号', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '流程状态', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'create_at', name: '申请日期', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'current_node', name: '当前节点', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'proposer', name: '申请人', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'owner', name: '负责人', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'shop_platform', name: '平台大类', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'shop_platform_subtype', name: '平台小类', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'license_name', name: '营业执照', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'legal_person_name', name: '法人', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.SUPPLIER_SETTLEMENT_APPROVAL]: [
    { key: 'id', name: 'ID', width: 60, freeze: 0, is_show: false, is_sort: false },
    { key: 'seq', name: '序号', width: 60, freeze: 0, is_show: true, is_sort: false },
    { key: 'number', name: '供应商编码', width: 0, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'supplier_name', name: '供应商名称', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'business_scale_string', name: '经营规模', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'mainCategorie', name: '主营类目', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'company_type_string', name: '公司类型', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'audit_type_string', name: '审核类型', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'audit_status_string', name: '审核状态', width: 0, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'badge' } },
    { key: 'audit_opinion', name: '审核备注', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'applicant', name: '申请人', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'application_at', name: '申请时间', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 200, freeze: 2, is_show: true },
  ],
  [PageType.SUPPLIER_INFO]: [
    { key: 'id', name: 'ID', width: 60, freeze: 0, is_show: false, is_sort: false },
    { key: 'seq', name: '序号', width: 60, freeze: 0, is_show: true, is_sort: false },
    { key: 'number', name: '供应商编码', width: 150, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'supplier_name', name: '供应商名称', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'company_type_string', name: '供应商类别', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'mainCategorie', name: '主营类目', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'business_scale_string', name: '经营规模', width: 100, freeze: 0, is_show: true, is_sort: false },
    { key: 'status_string', name: '状态', width: 70, freeze: 0, is_show: true, is_sort: false },
    { key: 'modifier_user', name: '更新人', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'modified_at', name: '更新时间', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'join_at', name: '供应商加入时间', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'srm_supplier_id', name: 'SRM供应商编码', width: 110, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'srm_company_supplier_id', name: 'SRM子公司编码', width: 110, freeze: 0, is_show: true, is_sort: false },
    { key: 'srm_is_pushed_string', name: '同步状态', width: 100, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'badge' } },
    { key: 'srm_push_time', name: '同步时间', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'operate', name: '操作', width: 200, freeze: 2, is_show: true },
  ],
  [PageType.Country_Region]: [
    // { key: 'id', name: 'ID', width: 60, freeze: 0, is_show: false, is_sort: false },
    // { key: 'seq', name: '序号', width: 60, freeze: 0, is_show: true, is_sort: false },
    // { key: 'number', name: '标志', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'code', name: '国家/地区编码', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'country_region_name', name: '国家/地区名称', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'area_names', name: '区域名称', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'used_language_name', name: '使用语言', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'create_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'modified_at', name: '最后修改时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.Currency]: [
    // { key: 'number', name: '货币编码', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'currency_name', name: '货币名称', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'currency_code', name: '货币代码', width: 0, freeze: 0, is_show: true, is_sort: true },
    // { key: 'number', name: '单据显示货币符号', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'symbol', name: '货币符号', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'price_digits', name: '单价精度', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'amount_digits', name: '金额精度', width: 0, freeze: 0, is_show: true, is_sort: false },
    // { key: 'number', name: '优先级', width: 0, freeze: 0, is_show: true, is_sort: false },
    // { key: 'number', name: '中间币', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'round_type_str', name: '舍入类型', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'create_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'modified_at', name: '最后修改时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.ExchangeRate]: [
    { key: 'number', name: '汇率类型', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'supplier_name', name: '原币', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'company_type_string', name: '目标币', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'number', name: '直接汇率', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'number', name: '间接汇率', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'number', name: '生效日期', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'number', name: '失效日期', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'status_string', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'modified_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'modified_at', name: '最后修改时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 350, freeze: 2, is_show: true },
  ],
  [PageType.LanguageTB]: [
    // { key: 'logo_url', name: '标志', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'code', name: '语言编码', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'language_name', name: '语言名称', width: 0, freeze: 0, is_show: true, is_sort: false },
    // { key: 'text_direction_str', name: '文本方向', width: 0, freeze: 0, is_show: true, is_sort: false },
    // { key: 'is_default_language', name: '默认语言', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'create_at', name: '创建时间', width: 0, freeze: 0, is_show: true, is_sort: true },
    { key: 'modified_at', name: '最后修改时间', width: 0, freeze: 0, is_show: true, is_sort: true },
  ],

  [PageType.CATEGORY_MANAGEMENT]: [
    { key: 'category_name', name: '类目名称', freeze: 0, is_show: true, is_sort: false, treeNode: true },
    { key: 'number', name: '类目编号', width: 120, freeze: 0, is_show: true, is_sort: false },
    { key: 'code', name: '类目编码', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'category_template_name', name: '类目模板', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'status', name: '状态', width: 100, freeze: 0, is_show: true, is_sort: true },
    { key: 'create_at', name: '创建时间', width: 160, freeze: 0, is_show: true, is_sort: true },
    { key: 'update_at', name: '最后修改时间', width: 160, freeze: 0, is_show: true, is_sort: true },
    { key: 'operate', name: '操作', width: 120, freeze: 2, is_show: true },
  ],
  [PageType.SUPPLIER_PRODUCT_LIBRARY]: [
    { key: 'seq', name: ' 序号 ', width: 50, freeze: 0, is_show: true, is_sort: false },
    { key: 'images_view_url', name: ' 商品主图 ', width: 70, freeze: 0, is_show: true, is_sort: false },
    { key: 'supplier_product_number', name: ' 供应商商品编码 ', width: 140, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'product_number', name: ' 平台商品编码 ', width: 130, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'product_name', name: ' 商品名称(中) ', width: 120, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'product_name_en', name: ' 商品名称(英) ', width: 130, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'is_white_brand_string', name: ' 是否白牌 ', width: 80, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'brand_name', name: ' 商品品牌 ', width: 100, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'category_string', name: ' 商品类目 ', width: 200, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'declared_purchase_tax_price', name: ' 申报采购单价(含税) ', width: 150, freeze: 0, is_show: true, is_sort: true, formatter: priceFormatter },
    { key: 'agreed_purchase_tax_price', name: ' 议定采购单价(含税) ', width: 150, freeze: 0, is_show: true, is_sort: true, formatter: priceFormatter },
    { key: 'selection_status_string', name: ' 选品状态 ', width: 100, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'badge' } },
    // { key: 'selection_time', name: ' 选品时间 ', width: 140, freeze: 0, is_show: true, is_sort: true, formatter: defaultFormatter },
    // { key: 'selection_person', name: ' 选品人 ', width: 100, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'supplier_name', name: ' 供应商名称 ', width: 200, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'supplier_number', name: ' 供应商编码 ', width: 140, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'submit_audit_name', name: ' 提审人 ', width: 100, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'submit_audit_at', name: ' 提审时间 ', width: 140, freeze: 0, is_show: true, is_sort: true, formatter: defaultFormatter },
    { key: 'status_string', name: ' 审核结果 ', width: 100, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'badge' } },
    { key: 'audit_notes', name: ' 备注 ', width: 150, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'plm_product_code', name: 'PLM商品编码 ', width: 140, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'jst_product_code', name: ' 聚水潭商品编码 ', width: 140, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'selection_notes', name: ' 选品意见 ', width: 150, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'create_at', name: ' 创建时间 ', width: 140, freeze: 0, is_show: true, is_sort: true, formatter: defaultFormatter },
    { key: 'modified_at', name: ' 修改时间 ', width: 140, freeze: 0, is_show: true, is_sort: true, formatter: defaultFormatter },
    { key: 'operate', name: ' 操作 ', width: 300, freeze: 2, is_show: true },
  ],
  [PageType.QUALIFICATION_CERTIFICATE]: [
    { key: 'seq', name: '序号', width: 50, freeze: 0, is_show: true, is_sort: false },
    { key: 'certificate_code', name: '资质/证书编码', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'certificate_name', name: '资质/证书', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'manufacturer_name', name: '生产商', width: 150, freeze: 0, is_show: true, is_sort: false },
    { key: 'file_count', name: '资质/证书文件', width: 80, freeze: 0, is_show: true, is_sort: false },
    { key: 'validity_period', name: '资质有效期', width: 180, freeze: 0, is_show: true, is_sort: false },
    { key: 'expired_status_str', name: '过期状态', width: 0, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'badge' } },
    { key: 'modified_at', name: '上传时间', width: 170, freeze: 0, is_show: true, is_sort: true },
    { key: 'supplier_name', name: '所属供应商', width: 200, freeze: 0, is_show: true },
    { key: 'supplier_code', name: '供应商编码', width: 0, freeze: 0, is_show: true },
    { key: 'approval_status_str', name: '审核状态', width: 0, freeze: 0, is_show: true, is_sort: true, cellRender: { name: 'badge' } },
    { key: 'approval_content', name: '审核备注', width: 100, freeze: 0, is_show: true },
    { key: 'operate', name: '操作', width: 140, freeze: 2, is_show: true },
  ],
  [PageType.ProductLabel]: [
    { key: 'name', name: '商品标签', width: 0, freeze: 0, is_show: true, is_sort: false },
    { key: 'update_at', name: '更新时间', width: 0, freeze: 0, is_show: true, is_sort: true },
  ],
  [PageType.PRODUCT_AUDIT]: [
    { key: 'seq', name: '序号', width: 50, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'images_view_url', name: '商品主图', width: 70, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'supplier_product_number', name: '供应商商品编码', width: 140, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'product_number', name: '平台商品编码', width: 130, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'product_name', name: '商品名称(中)', width: 100, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'product_name_en', name: '商品名称(英)', width: 130, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'is_white_brand_string', name: ' 是否白牌 ', width: 80, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'brand_name', name: '商品品牌', width: 86, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'category_string', name: '商品类目', width: 200, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'declared_purchase_tax_price', name: '申报采购单价(含税)', width: 150, freeze: 0, is_show: true, is_sort: true, formatter: priceFormatter },
    { key: 'agreed_purchase_tax_price', name: '议定采购单价(含税)', width: 150, freeze: 0, is_show: true, is_sort: true, formatter: priceFormatter },
    { key: 'store_supply_price', name: '供货价', width: 150, freeze: 0, is_show: true, is_sort: true, formatter: priceFormatter },
    { key: 'first_batch_quantity', name: '首批新品采购量', width: 150, freeze: 0, is_show: true, is_sort: true, formatter: defaultFormatter },
    // { key: 'shipping_fee', name: '运费', width: 150, freeze: 0, is_show: true, is_sort: true, formatter: priceFormatter },
    { key: 'shipment_time', name: '发货时间', width: 140, freeze: 0, is_show: true, is_sort: true, formatter: defaultFormatter },
    { key: 'expected_delivery_date', name: '预计交期', width: 140, freeze: 0, is_show: true, is_sort: true, formatter: defaultFormatter },
    { key: 'selection_notes', name: '选品意见', width: 140, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'submit_audit_name', name: '提审人', width: 140, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'submit_audit_at', name: '提审时间', width: 140, freeze: 0, is_show: true, is_sort: true, formatter: defaultFormatter },
    { key: 'status_string', name: '审核状态', width: 140, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'badge' } },
    { key: 'audit_name', name: '审核人员', width: 140, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'audit_time', name: '审核时间', width: 140, freeze: 0, is_show: true, is_sort: true, formatter: defaultFormatter },
    { key: 'audit_notes', name: '备注', width: 140, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'supplier_name', name: '供应商名称', width: 220, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'supplier_number', name: '供应商编码', width: 150, freeze: 0, is_show: true, is_sort: false, formatter: defaultFormatter },
    { key: 'create_at', name: '创建时间', width: 140, freeze: 0, is_show: true, is_sort: true, formatter: defaultFormatter },
    { key: 'modified_at', name: '修改时间', width: 140, freeze: 0, is_show: true, is_sort: true, formatter: defaultFormatter },
    { key: 'operate', name: '操作', width: 200, freeze: 2, is_show: true },
  ],
  [PageType.PRODUCT_STOCK]: [
    { key: 'main_image', name: '商品主图', width: 80, freeze: 0, is_show: true, is_sort: false },
    { key: 'supplier_product_number', name: '供应商商品编码', width: 200, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'product_number', name: '平台商品编码', width: 200, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'product_name', name: '商品名称', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'category_string', name: '商品类目', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'supplier_name', name: '供应商名称', width: 200, freeze: 0, is_show: true, is_sort: false },
    { key: 'jst_inventory_num', name: '仓库实际库存', width: 130, freeze: 0, is_show: true, is_sort: false },
    { key: 'modified_at', name: '更新时间', width: 150, freeze: 0, is_show: true, is_sort: true },
    { key: 'jst_product_code', name: '聚水潭商品编码', width: 200, freeze: 0, is_show: true, is_sort: false, cellRender: { name: 'copy' } },
    { key: 'operate', name: '操作', width: 80, freeze: 2, is_show: true },
  ],
}
