import vue from '@vitejs/plugin-vue'
import path from 'path'
import UnoCSS from 'unocss/vite'
import vueJsx from '@vitejs/plugin-vue-jsx'
import autoImport from 'unplugin-auto-import/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'
import { defineConfig, loadEnv } from 'vite'
import ZipPack from 'vite-plugin-zip-pack'
import { VitePWA } from 'vite-plugin-pwa'
// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_BASE_API, VITE_APP_ENV /* , VITE_APP_VERSION */ } = env
  // 定义 Hangfire 相关的 API 地址，从环境变量读取，并设置备用值
  // 备用顺序：VITE_HANGFIRE_API_XXX -> VITE_APP_BASE_API -> http://localhost:XXXX
  const hangfireApi8098 = VITE_APP_BASE_API || 'http://localhost:8098'
  const hangfireApi8099 = VITE_APP_BASE_API || 'http://localhost:8099'
  const tasksHangfireApi = VITE_APP_BASE_API || 'http://localhost:8098'
  return {
    base: '/', // 统一使用绝对路径，避免深层路径下的静态资源加载问题
    plugins: [
      vue(),
      vueJsx(),
      UnoCSS(),
      autoImport({
        imports: ['vue', 'vue-router'],
        eslintrc: {
          enabled: true, // <-- this
          globalsPropValue: true,
        },
        dirs: ['src/hook'],
      }),
      Components({
        resolvers: [AntDesignVueResolver({ importStyle: false })],
      }),
      VitePWA({
        workbox: {
          navigateFallbackDenylist: [/^\/api/, /^\/hangfire/, /^\/hangfire99/, /^\/tasks/],
          cleanupOutdatedCaches: true,
          skipWaiting: true, // 关键：跳过等待阶段
          clientsClaim: true, // 关键：立即接管客户端
          // 可以添加更多缓存策略
          runtimeCaching: [
            {
              urlPattern: ({ request }) => request.destination === 'script',
              handler: 'StaleWhileRevalidate',
              options: {
                cacheName: 'scripts-cache',
              },
            },
          ],
        },
        registerType: 'autoUpdate',
        includeAssets: ['favicon.ico', 'robots.txt'],
        devOptions: {
          enabled: true,
          type: 'module',
        },
        manifest: {
          name: 'SRM',
          short_name: 'SRM',
          start_url: '/',
          display: 'standalone',
          background_color: '#ffffff',
          theme_color: '#409eff',
          icons: [
            {
              src: '/pwa-192x192.png',
              sizes: '192x192',
              type: 'image/png',
            },
            {
              src: '/pwa-512x512.png',
              sizes: '512x512',
              type: 'image/png',
            },
          ],
        },
      }),
      ZipPack({
        outDir: './dist',
        outFileName: 'dist.zip',
        pathPrefix: 'dist',
      }),
    ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "@/assets/style/mixin.scss" as *;',
        },
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@img': path.resolve(__dirname, 'src/assets/image'),
      },
    },
    server: {
      port: 8899,
      host: true,
      open: true,
      proxy: {
        '/api': {
          target: VITE_APP_BASE_API,
          changeOrigin: true,
          rewrite: (p) => p.replace(/\/api/, '/'),
        },
        '/XY': {
          target: VITE_APP_BASE_API,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/XY/, '/XY'),
        },
        '/hangfire': {
          target: hangfireApi8098,
          changeOrigin: true, // 解决跨域问题
          // secure: false, // 如果后端是https且证书有问题，可以加上这个
        },
        '/hangfire99': {
          target: hangfireApi8099,
          changeOrigin: true,
          // 如果后端路径还是 /hangfire，你需要重写路径
          rewrite: (path) => path.replace(/^\/hangfire99/, '/hangfire'),
        },
        '/tasks': {
          target: tasksHangfireApi, // 确保这个目标地址是正确的
          changeOrigin: true,
          // 通常，后端的路径就是 /tasks/hangfire，所以这里一般不需要 rewrite
          // 如果后端实际接收的路径没有 /tasks 前缀，才需要 rewrite
          // rewrite: (path) => path.replace(/^\/tasks/, ''),
        },
      },
    },
    build: {
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: VITE_APP_ENV === 'production',
          drop_debugger: VITE_APP_ENV === 'production',
        },
      },
      chunkSizeWarningLimit: 1500,
      assetsDir: 'assets',
      rollupOptions: {
        output: {
          manualChunks: {
            'vue-core': ['vue', 'vue-router'],
            'ant-design-vue': ['ant-design-vue'],
            charts: ['@antv/g2'],
            utils: ['dayjs', 'axios'],
            common: ['@/components/BaseTable/index.vue', '@/components/Form.vue'],
          },
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            const fileName = assetInfo.names?.[0] || assetInfo.name || 'unknown'
            const info = fileName.split('.')
            let extType = info[info.length - 1]
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(fileName)) {
              extType = 'media'
            } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(fileName)) {
              extType = 'img'
            } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(fileName)) {
              extType = 'fonts'
            }
            return `assets/${extType}/[name]-[hash].[ext]`
          },
        },
      },
    },
  }
})
