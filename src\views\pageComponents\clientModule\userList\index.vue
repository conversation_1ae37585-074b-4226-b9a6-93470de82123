<template>
  <div class="main">
    <div class="mask" v-if="isShowLoadingMask">
      <a-spin />
    </div>
    <div class="main-content">
      <!-- 左侧组织架构面板 -->
      <ArchitectureLeftPanel
        ref="architectureRef"
        :selected-id="selectedDeptId"
        :check-permission="checkArchPermission"
        @select-change="handleArchSelectChange"
        @add-dept="handleAddDept"
        @edit-dept="handleEditDept"
        @view-dept="handleViewDept"
        @dept-operation-success="handleDeptOperationSuccess"
      />

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <SearchForm ref="formRef" v-model:form="formArr" :page-type="PageType.USER_MANAGE" @search="search" @setting="tableRef?.showTableSetting()" :clearCb="resetFormCb"></SearchForm>

        <BaseTable
          ref="tableRef"
          :page-type="PageType.USER_MANAGE"
          v-model:form="formArr"
          :get-list="GetList"
          :form-format="formFormat"
          :data-format="dataFormat"
          :is-index="true"
          :auto-search="hasUserManagementPermission"
        >
          <template #left-btn>
            <!-- <a-button
          type="primary"
          :disabled="!btnPermission[21001]"
          @click="tapManipulate('add')">
          新建用户
        </a-button> -->
            <a-button @click="tapManipulate('sync')">同步信息</a-button>
          </template>
          <!-- Custom slot templates for special column rendering -->
          <template #operate="{ row }">
            <a-button type="text" :disabled="!btnPermission[21002]" @click="tapManipulate('view', row)" class="mr-10px">查看</a-button>
            <a-button type="text" :disabled="!btnPermission[21003]" @click="tapManipulate('compiler', row)">编辑</a-button>
          </template>

          <!-- <template #customer_name="{ row }">
            {{ supplierNameMap[row.customer_id] || '--' }}
          </template> -->

          <template #status="{ row }">
            <div v-if="row.status !== null">
              <span :class="row.status === 1 ? 'status-enabled' : 'status-disabled'">
                {{ row.status === 1 ? '启用' : '停用' }}
              </span>
            </div>
            <span v-else>--</span>
          </template>

          <template #role_names="{ row }">
            <a-tag v-for="item in row['role_names']" :key="item">{{ item }}</a-tag>
          </template>
          <!-- 
          <template #source_type="{ row }">
            <span>{{ formatOptionLabel(row.source_type, sourceOption) || '--' }}</span>
          </template> -->

          <!-- Default value templates for other columns -->
          <template #uid="{ row }">
            <span>{{ row.uid || '--' }}</span>
          </template>

          <template #job_id="{ row }">
            <span>{{ row.job_id || '--' }}</span>
          </template>

          <template #real_name="{ row }">
            <span>{{ row.real_name || '--' }}</span>
          </template>

          <template #department_name="{ row }">
            <span>{{ row.department_name || '--' }}</span>
          </template>

          <template #position_name="{ row }">
            <span>{{ row.position_name || '--' }}</span>
          </template>

          <template #leader_name="{ row }">
            <span>{{ row.leader_name || '--' }}</span>
          </template>

          <template #create_at="{ row }">
            <span>{{ row.create_at || '--' }}</span>
          </template>

          <template #update_at="{ row }">
            <span>{{ row.update_at || '--' }}</span>
          </template>
        </BaseTable>

        <a-modal v-model:open="visibleData.isShow" :title="visibleData.title">
          <div class="modalContent">{{ visibleData.content }}</div>
          <template #footer>
            <a-button v-if="visibleData.isConfirmBtn" style="margin-right: 10px" type="primary" @click="visibleData.okFn">{{ visibleData.confirmBtnText }}</a-button>
            <a-button v-if="visibleData.isCancelBtn" @click="visibleData.isShow = false">取消</a-button>
          </template>
        </a-modal>

        <ExtraFormDrawer ref="extraFormDrawerRef" @query="search" />
        <InnerFormDrawer ref="innerFormDrawerRef" @query="search" />
        <UserInfoDrawer ref="userInfoDrawerRef" />
        <SimpleUserInfoDrawer ref="simpleUserInfoDrawerRef" />
      </div>
    </div>

    <!-- 部门管理弹窗 -->
    <DepartmentFormDrawer ref="deptFormDrawerRef" :parent-id="selectedParentDeptId" :company-id="selectedCompanyId" @success="handleDeptFormSuccess" />
    <DepartmentDetailDrawer ref="deptDetailDrawerRef" @view-dept="handleViewDept" />

    <!-- 新增用户组件 -->
    <AddMembers ref="addMembersRef" @query="search" />

    <!-- 编辑用户组件 -->
    <EditMembers ref="editMembersRef" @query="search" />
  </div>
</template>
<script lang="ts" setup>
import { PageType } from '@/common/enum'
import { GetList, GetDepartmentContactTreeList, SyncCompanyInfo } from '@/servers/UserManagerNew'
import { Delete, ResetPwd } from '@/servers/UserManager'
import { GetRoleSelectOption } from '@/servers/RoleNew'
import { message } from 'ant-design-vue'
import { onMounted, ref } from 'vue'
import { checkPagePermission, buttonDebounce } from '@/utils'
import type { TreeNode } from '@/servers/CompanyArchitecture'
import ExtraFormDrawer from './components/ExtraFormDrawer.vue'
import InnerFormDrawer from './components/InnerFormDrawer.vue'
import UserInfoDrawer from './components/UserInfoDrawer.vue'
import SimpleUserInfoDrawer from './components/SimpleUserInfoDrawer.vue'
import ArchitectureLeftPanel from './components/ArchitectureLeftPanel.vue'
import DepartmentFormDrawer from './components/DepartmentFormDrawer.vue'
import DepartmentDetailDrawer from './components/DepartmentDetailDrawer.vue'
import AddMembers from './components/AddMembers.vue'
import EditMembers from './components/EditMenbers.vue'

const extraFormDrawerRef = ref()
const innerFormDrawerRef = ref()
const userInfoDrawerRef = ref()
const simpleUserInfoDrawerRef = ref()
const architectureRef = ref()
const deptFormDrawerRef = ref()
const addMembersRef = ref()
const editMembersRef = ref()
const deptDetailDrawerRef = ref()

const { btnPermission } = usePermission()

// 检查用户管理权限
const hasUserManagementPermission = ref(false)

// 组织架构相关状态
const selectedDeptId = ref<string>('')
const selectedCompanyId = ref<string>('')
const selectedParentDeptId = ref<string>('')
const currentArchNode = ref<TreeNode | null>(null)

// 视图模式状态
const viewMode = ref(1) // 1: 展示全部成员, 2: 仅展示部门直属成员

const visibleDataStr = () => ({
  isShow: false,
  isConfirmBtn: true,
  isCancelBtn: true,
  confirmBtnText: '确定',
  title: '',
  content: '',
  okFn: () => {
    visibleData.value.isShow = false
  },
})
const visibleData: any = ref(visibleDataStr())

const tableRef = ref()
const formRef = ref()
const search = () => {
  if (tableRef.value && tableRef.value.search) {
    tableRef.value.search()
  }
}

const supplierNameMap = ref({} as any)
// const departmentMap = ref<any>({})

// 独立的查询参数，用于架构选择
const queryParams = ref({
  company_id: null as string | null,
  dept_id: null as string | null,
})

const isShowLoadingMask = ref(false)

// 查找父级企业ID的辅助函数
const findParentCompanyId = (node: TreeNode): string | null => {
  // 如果当前节点就是企业节点，返回其ID
  if (node.type === '企业=1' || (node as any).originalType === 1) {
    return String(node.id)
  }

  // 这里可以通过架构树数据向上查找父级企业
  // 由于当前架构树结构，部门节点的company_id应该从架构树中获取
  // 简化处理：直接使用当前选中的企业ID
  return selectedCompanyId.value
}

// 表单字段映射函数 - 将前端表单字段映射为API期望的字段
const formFormat = (obj: any) => {
  const formattedObj = { ...obj }

  // 设置默认状态为启用（如果用户没有选择状态）
  if (formattedObj.status === null || formattedObj.status === undefined) {
    formattedObj.status = 1 // 默认显示启用状态的用户
  }

  // enterprise_code字段直接来自表单，不需要额外添加

  // 优先使用架构选择的查询参数
  if (queryParams.value.company_id) {
    formattedObj.company_id = queryParams.value.company_id
  } else if (formattedObj.subcompanyid1) {
    // 如果没有架构选择，则使用表单中的公司选择
    formattedObj.company_id = formattedObj.subcompanyid1
  }

  // 添加部门ID参数支持
  if (queryParams.value.dept_id) {
    formattedObj.dept_id = queryParams.value.dept_id
  }

  // 删除原始字段
  delete formattedObj.subcompanyid1

  // 将 departmentid 映射为 department_id
  if (formattedObj.departmentid) {
    formattedObj.department_id = formattedObj.departmentid
    delete formattedObj.departmentid
  }

  // 处理搜索关键字 - 直接使用接口的 keyword 参数
  if (formattedObj.search_keyword) {
    const keyword = formattedObj.search_keyword.trim()
    if (keyword) {
      // 直接使用 keyword 参数，接口会自动在账户编号/工号/用户编号/用户名/手机/邮箱等字段中搜索
      formattedObj.keyword = keyword
    }
    delete formattedObj.search_keyword
  }

  return formattedObj
}

const dataFormat = (data: any[]) => {
  const department_id = [...new Set(data.map((v: any) => v.department_id).filter((v: any) => v))]
  if (department_id.length) {
    // GetUserDeptNameList({
    //   department_id,
    // }).then((res) => {
    //   res.data.forEach((v) => {
    //     departmentMap.value[v.department_id] = v.department_name
    //   })
    // })
  }
  return data
}

const formArr: any = ref([
  {
    label: '展示全部成员',
    value: 1,
    type: 'select',
    options: [
      { label: '展示全部成员', value: 1 },
      { label: '仅展示部门直属成员', value: 2 },
    ],
    key: 'enterprise_code',
    onChange: (value: any) => {
      viewMode.value = value
      handleViewModeChange()
    },
  },
  {
    label: '搜索姓名',
    value: null,
    type: 'input',
    options: [],
    key: 'real_name',
    placeholder: '请输入姓名',
  },
  {
    width: 190,
    label: '搜索账号/工号/手机/邮箱',
    value: null,
    type: 'input',
    options: [],
    key: 'search_keyword',
    showTooltip: true,
    placeholder: '搜索账号/工号/手机/邮箱',
  },
  {
    label: '角色',
    value: null,
    type: 'select',
    options: [],
    key: 'role_id',
    showSearch: true,
  },
  // {
  //   label: '状态',
  //   value: 1,
  //   type: 'select',
  //   options: [
  //     { label: '启用', value: 1 },
  //     { label: '停用', value: 0 },
  //   ],
  //   key: 'status',
  //   showSearch: true,
  // },
  // {
  //   label: '所属公司',
  //   value: null,
  //   type: 'select_one',
  //   options: [],
  //   search: true,
  //   key: 'subcompanyid1',
  //   onChange: () => {
  //     formArr.value.find((e) => e.key === 'departmentid').value = null
  //     getDepartmentTreeList()
  //   },
  // },
  // {
  //   label: '所在部门',
  //   value: null,
  //   type: 'select_Tree',
  //   fieldNames: {
  //     children: 'childrenList',
  //     label: 'departmentname',
  //     value: 'departmentid',
  //   },
  //   options: [],
  //   key: 'departmentid',
  //   onClick: () => {
  //     if (!formArr.value.find((e) => e.key === 'subcompanyid1').value) {
  //       message.info('请先选择所属公司')
  //       return false
  //     }
  //   },
  // },
  // {
  //   label: '创建时间',
  //   value: null,
  //   type: 'range-picker',
  //   key: 'create_at',
  //   formKeys: ['create_start_at', 'create_end_at'],
  //   placeholder: ['创建开始时间', '创建结束时间'],
  // },
  // {
  //   label: '修改时间',
  //   value: null,
  //   type: 'range-picker',
  //   key: 'update_at',
  //   formKeys: ['update_start_at', 'update_end_at'],
  //   placeholder: ['修改开始时间', '修改结束时间'],
  // },
])
const role_Arr = ref([])

const initScreening = () => {
  const obj = JSON.parse(localStorage.getItem('screeningObj') || '{}')
  if (obj.USER_MANAGE) {
    const arr: any[] = []
    obj.USER_MANAGE.forEach((x: any) => {
      formArr.value.forEach((y: any) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value.forEach((item: any) => {
      item.isShow = true
    })
  }
}

onMounted(() => {
  // 检查当前用户是否有权限访问用户管理页面
  hasUserManagementPermission.value = checkPagePermission('/userLists')

  if (hasUserManagementPermission.value) {
    getRoleList()
    search()
    initScreening()
  } else {
    console.warn('用户无权限访问用户管理页面，跳过接口调用')
  }
})

// 点击批量备货按钮 - 已禁用，状态改为只读显示
// const tapBatch = (status) => {
//   if (tableRef.value.checkItemsArr?.length) {
//     const arr = [] as any[]
//     tableRef.value.checkItemsArr.forEach((item) => {
//       arr.push(item.id)
//     })
//     tapSwitch(status, arr)
//   } else {
//     message.info('请勾选对应项~')
//   }
// }

const tapManipulateCore = async (type: string, row: any = '') => {
  visibleData.value = visibleDataStr()
  switch (type) {
    case 'add': {
      // 使用新的 AddMembers 组件
      addMembersRef.value?.show(selectedCompanyId.value || '')
      break
    }

    case 'view':
      // 使用新的简洁用户详情组件
      simpleUserInfoDrawerRef.value.showDrawer(row)
      break
    case 'compiler':
      // 使用新的 EditMembers 组件
      editMembersRef.value?.showDrawer(row)
      break
    case 'removes':
      visibleData.value.isShow = true
      visibleData.value.title = '删除用户：'
      visibleData.value.content = `
      即将删除该用户的帐号，删除后：

          * 该用户将无法访问系统的任何功能或数据。
          * 用户的操作历史记录和相关数据将保留在系统中。

      请在执行此操作前确认：

          * 该用户的相关工作已妥善移交或完成。

      此操作不可恢复，确定要删除该用户的帐号吗？`
      visibleData.value.confirmBtnText = '确定'
      visibleData.value.isCancelBtn = true
      visibleData.value.okFn = () => {
        deleteRole(row.id)
      }
      break
    case 'resetPassword':
      visibleData.value.isShow = true
      visibleData.value.title = '重置密码'
      visibleData.value.content = `
      即将为该用户重置密码，重置后：
        * 用户的密码将被重置为系统的初始默认密码。
        * 用户需使用默认密码重新登录并更新为新的个人密码。
      确定要重置该用户的密码吗？`
      visibleData.value.confirmBtnText = '确定'
      visibleData.value.isCancelBtn = true
      visibleData.value.okFn = () => {
        resetPwd(row.id)
      }
      break
    case 'sync':
      visibleData.value.isShow = true
      visibleData.value.title = '同步信息'
      visibleData.value.content = `是否确认同步信息？同步时间视数据量而定，请耐心等候。`
      visibleData.value.confirmBtnText = '确认'
      visibleData.value.isCancelBtn = true
      visibleData.value.okFn = async () => {
        visibleData.value.isShow = false
        isShowLoadingMask.value = true
        await SyncCompanyInfo()
        message.success('同步信息成功')
        isShowLoadingMask.value = false
      }
      break
    default:
      break
  }
}

// 添加防抖的用户操作入口函数
const tapManipulate = buttonDebounce(tapManipulateCore, 300)

// 状态切换功能已禁用，状态改为只读显示
// const tapSwitch = (status, arr) => {
//   if (!status) {
//     updateExtraStatus({ user_ids: arr, status: 1 })
//   } else {
//     visibleData.value.isShow = true
//     visibleData.value.title = '停用用户：'
//     visibleData.value.content = `
//     停用后，该用户将无法访问系统的任何功能或数据。
//     确定要停用该用户的帐号吗？`
//     visibleData.value.confirmBtnText = '停用'
//     visibleData.value.isCancelBtn = true
//     visibleData.value.okFn = () => {
//       updateExtraStatus({ user_ids: arr, status: 0 })
//     }
//   }
// }

// 批量停用/启用功能已禁用，状态改为只读显示
// const updateExtraStatus = (obj) => {
//   UpdateOuterStatus(obj).then(() => {
//     visibleData.value.isShow = false
//     message.success('设置成功~')
//     tableRecords.value = []
//     tableRef.value.refresh()
//   })
// }

// 删除外部用户核心逻辑
const deleteRoleCore = (id: any) => {
  Delete({ id }).then(() => {
    visibleData.value.isShow = false
    message.success('删除成功')
    tableRef.value.refresh()
  })
}

// 添加防抖的删除用户函数
const deleteRole = buttonDebounce(deleteRoleCore, 1000)

// 修改密码
// const setUpdatePwd = () => {
//   UpdatePwd({
//     id: formData.value.id,
//     old_password: formData.value.old_password,
//     new_password: formData.value.new_password,
//   }).then(res => {
//     message.success('修改成功');
//     isAddUser.value = false;
//     tapQueryForm();
//   });
// };

// 重置密码核心逻辑
const resetPwdCore = (id: any) => {
  ResetPwd({ id }).then((res) => {
    visibleData.value = visibleDataStr()
    setTimeout(() => {
      visibleData.value.isShow = true
      visibleData.value.title = '重置密码'
      visibleData.value.content = `
    已成功重置该用户的密码，重置后的密码为：

            ${res.data.newPwd}

    请及时将密码发给该用户，并通知其登录后修改密码。`
      visibleData.value.isCancelBtn = false
      visibleData.value.confirmBtnText = '关闭'
    }, 300)
  })
}

// 添加防抖的重置密码函数
const resetPwd = buttonDebounce(resetPwdCore, 1000)

// 获取公司树状下拉框
// const getCompanyList = () => {
//   GetCompanyList().then((res) => {
//     res.data.forEach((x) => {
//       x.label = x.company_name
//       x.value = x.company_id
//     })
//     formArr.value.forEach((item) => {
//       if (item.key == 'subcompanyid1') {
//         item.options = res.data
//       }
//     })
//   })
// }

// 获取角色列表
const getRoleList = () => {
  GetRoleSelectOption({ scope: 1 }).then((res) => {
    const roleOptions = res.data
      // .filter((e) => e.status == 1)
      .map((e: any) => ({
        label: e.role_name,
        value: e.role_id,
      }))
    formArr.value.forEach((item: any) => {
      if (item.key == 'role_id') {
        item.options = roleOptions
      }
    })
    role_Arr.value = res.data // 保持原有的role_Arr变量
  })
}
// 获取部门树状下拉框(内部)
const getDepartmentTreeList = () => {
  const subcompanyItem = formArr.value.find((e: any) => e.key === 'subcompanyid1')
  const value = subcompanyItem?.value
  if (!value) {
    formArr.value.forEach((item: any) => {
      if (item.key == 'departmentid') {
        item.options = []
      }
    })
    return
  }
  GetDepartmentContactTreeList({ id: value, key: 'department' }).then((res) => {
    formArr.value.forEach((item: any) => {
      if (item.key == 'departmentid') {
        item.options = res.data
      }
    })
  })
}
const resetFormCb = () => {
  formArr.value.forEach((e: any) => {
    if (e.key === 'departmentid') {
      e.options = []
    }
    if (e.key !== 'enterprise_code') {
      e.value = null
    } else {
      // 重置时强制保持展示全部成员状态
      e.value = 1
    }
  })
}

// 视图模式变化处理
const handleViewModeChange = () => {
  console.log('视图模式变化:', viewMode.value)
  // 重置查询参数
  resetQuery()
  // 触发搜索
  search()
}

// 重置查询
const resetQuery = () => {
  formArr.value.forEach((item: any, index: number) => {
    if (index === 0) {
      // 第一个字段（enterprise_code）保持当前视图模式值
      item.value = viewMode.value
    } else {
      item.value = Array.isArray(item.value) ? [] : null
    }
  })
}

// ==================== 组织架构相关方法 ====================

// 权限检查函数
const checkArchPermission = (permission: string) => {
  // 临时返回 true 以便调试，实际应该根据真实权限配置
  // TODO: 需要根据实际的组织架构权限配置来实现
  // console.log('检查权限:', permission, '当前权限:', btnPermission)

  const permissionMap = {
    arch_add: true, // btnPermission[21001], // 新建用户权限作为新建部门权限
    arch_edit: true, // btnPermission[21003], // 编辑用户权限作为编辑部门权限
    arch_delete: true, // btnPermission[21003], // 编辑权限作为删除权限
    arch_view: true, // btnPermission[21002], // 查看用户权限作为查看部门权限
    arch_updown: true, // btnPermission[21003], // 编辑权限作为上移下移权限
  }
  return permissionMap[permission] || false
}

// 组织架构选择变化处理
const handleArchSelectChange = (id: string, data: TreeNode | null) => {
  selectedDeptId.value = String(id) // 确保是字符串类型
  currentArchNode.value = data

  if (data) {
    console.log('架构树选择:', id, '节点类型:', data.type)

    // 获取原始数字类型，兼容新旧格式
    const originalType = (data as any).originalType || data.type

    if (originalType === 1 || data.type === '企业=1') {
      // 企业节点 (type=1) - 只传 company_id，dept_id 设为 null
      selectedCompanyId.value = id
      queryParams.value.company_id = id
      queryParams.value.dept_id = null
      getDepartmentTreeList()
    } else if (originalType === 3 || data.type === '部门=3') {
      // 部门节点 (type=3) - 同时传 company_id 和 dept_id
      const companyId = findParentCompanyId(data)
      selectedCompanyId.value = companyId || id
      queryParams.value.company_id = companyId || id
      queryParams.value.dept_id = id
    } else {
      // 其他节点类型（单位等）
      selectedCompanyId.value = id
      queryParams.value.company_id = id
      queryParams.value.dept_id = null
    }

    // 自动触发搜索
    search()
  }
}

// 添加部门处理
const handleAddDept = (parentId?: string) => {
  selectedParentDeptId.value = parentId || ''
  deptFormDrawerRef.value?.open()
}

// 编辑部门处理
const handleEditDept = (id: string) => {
  // 需要先获取部门详情数据
  const editData = {
    id,
    department_name: currentArchNode.value?.department_name || '',
    p_id: currentArchNode.value?.p_id || '',
    company_id: selectedCompanyId.value,
    header_ids: [],
    oa_id: currentArchNode.value?.oa_id || '',
  }
  deptFormDrawerRef.value?.open(editData)
}

// 查看部门详情处理
const handleViewDept = (id: string, nodeType: number) => {
  deptDetailDrawerRef.value?.open(id, nodeType)
}

// 部门表单提交成功处理
const handleDeptFormSuccess = () => {
  // 刷新组织架构树
  architectureRef.value?.refresh()
  // 刷新用户列表
  search()
  message.success('操作成功')
}

// 部门操作成功处理（删除、移动等）
const handleDeptOperationSuccess = () => {
  // 刷新用户列表
  search()
}
</script>
<style lang="scss" scoped>
.userRoleBox {
  padding-right: 40px;

  .li {
    display: flex;
    align-items: center;
    margin-top: 24px;
    font-size: 16px;
    color: #000;

    .label {
      width: 120px;
      margin-right: 30px;
      text-align: right;

      .text {
        position: relative;

        .icon_i {
          position: absolute;
          top: 50%;
          left: -15px;
          padding-top: 7px;
          font-size: 12px;
          color: red;
          transform: translateY(-50%);
        }
      }
    }

    .input {
      flex: 1;
    }

    .checkedBox {
      flex: 1;
    }

    .select {
      flex: 1;
    }

    .treeSelect {
      flex: 1;
    }
  }
}

.modalContent {
  font-size: 14px;
  word-break: break-word;
  white-space: pre-wrap;
}

.status-enabled {
  font-weight: 500;
  color: #52c41a;
}

.status-disabled {
  font-weight: 500;
  color: #ff4d4f;
}

.main {
  display: flex;
  flex-direction: column;
  height: 100%;

  .main-content {
    display: flex !important;
    flex: 1; /* 让内容区域占满剩余空间 */
    flex-direction: row !important; /* 确保是水平布局 */
    gap: 0; /* 移除默认间距 */
    align-items: stretch !important;
    height: calc(100vh - 140px) !important; /* 优化高度计算，减少空白 */
  }

  .right-content {
    display: flex !important;
    flex: 1 !important;
    flex-direction: column !important;
    width: calc(100% - 246px) !important; /* 总宽度减去左侧面板宽度和间距 */
    min-width: 0 !important; /* 防止flex子项溢出 */

    // 重置Form组件的flex布局
    :deep(.ant-form) {
      .flex {
        display: block !important;
      }

      margin-bottom: 16px; /* 减少表单底部间距 */
    }

    // 确保BaseTable正确显示
    :deep(.tableBox) {
      display: flex !important;
      flex: 1 !important;
      flex-direction: column !important;
      min-height: 0; /* 防止表格溢出 */

      .box {
        flex: 1;
        min-height: 0;
      }
    }
  }
}

.mask {
  position: absolute;
  inset: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(185 185 185 / 46%);
}
</style>
