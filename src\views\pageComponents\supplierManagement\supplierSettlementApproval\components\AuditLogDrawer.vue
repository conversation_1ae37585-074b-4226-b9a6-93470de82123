<template>
  <a-drawer title="审核记录" :width="672" v-model:open="visible" @close="handleClose">
    <div class="log-container" ref="logContainerRef">
      <a-timeline v-if="logList?.length > 0">
        <a-timeline-item v-for="(item, index) in logList" :key="index">
          <p class="font-bold">{{ item.op_at }}</p>
          <p>
            <span class="font-bold">{{ item.user_name }}</span>
            <span class="user-dept" v-show="item.user_department">[{{ item.user_department }}]</span>
          </p>
          <p class="operation-info">{{ item.op_type }}</p>

          <!-- 显示修改的字段 -->
          <div v-if="item.edits && item.edits.length > 0" class="log-details">
            <!-- <div class="operation-type">修改：</div> -->
            <div v-for="(edit, editIndex) in item.edits" :key="editIndex" class="log-detail-item">
              <!-- <span>{{ cleanFieldName(edit.name) }}：</span> -->
              <!-- <span class="old-value">{{ cleanFieldValue(edit.old_value) }}</span> -->
              <!-- <span class="arrow">→</span> -->
              <span class="new-value">{{ cleanFieldValue(edit.new_value) }}</span>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>

      <div v-if="logList?.length === 0 && !loading" class="empty-log">暂无审核记录</div>
      <div v-if="loading" class="loading-text">加载中...</div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { GetSupplierAuditInfo } from '@/servers/supplierSettlementApproval'

const visible = ref(false)
const logContainerRef = ref<HTMLElement | null>(null)
const loading = ref(false)

const logList = ref<any[]>([])

// 清理字段名称，去除引号
const cleanFieldName = (name: string) => {
  if (!name) return ''
  return name.replace(/"/g, '')
}

// 清理字段值，处理空值和特殊字符
const cleanFieldValue = (value: any) => {
  if (value === null || value === undefined || value === '') {
    // return '空'
    return ''
  }
  if (typeof value === 'string') {
    return value.replace(/"/g, '')
  }
  return String(value)
}

const loadData = async (supplierId: number) => {
  loading.value = true
  try {
    const res: any = await GetSupplierAuditInfo({
      supplierId,
    })
    if (res.code === 0 && res.success) {
      logList.value = res.data || []
    }
  } catch (error) {
    console.error('加载审核记录失败:', error)
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
  logList.value = []
}

// 打开抽屉
const open = async (supplierId: number) => {
  visible.value = true
  await loadData(supplierId)
}

// 暴露方法给父组件调用
defineExpose({
  open,
})
</script>

<style scoped lang="scss">
.log-container {
  height: 100%;
  overflow-y: auto;
}

.font-bold {
  font-weight: 600;
  margin-bottom: 4px;
}

.user-dept {
  color: #666;
  font-size: 12px;
  margin-left: 4px;
}

.operation-info {
  color: #333;
  margin-bottom: 8px;
}

.log-details {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 8px;
}

.operation-type {
  font-weight: 500;
  color: #666;
  margin-bottom: 4px;
  font-size: 12px;
}

.log-detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  span {
    &:first-child {
      color: #666;
      white-space: nowrap;
      margin-right: 4px;
    }
  }
}

.old-value {
  color: #ff4d4f;
  background: #fff2f0;
  padding: 2px 4px;
  border-radius: 2px;
  margin-right: 4px;
}

.new-value {
  color: #52c41a;
  background: #f6ffed;
  padding: 2px 4px;
  border-radius: 2px;
  margin-left: 4px;
}

.arrow {
  color: #999;
  margin: 0 4px;
}

.empty-log {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

.loading-text {
  text-align: center;
  color: #666;
  padding: 20px 0;
}

:deep(.ant-timeline-item-content) {
  margin-left: 20px;
}

:deep(.ant-timeline-item-tail) {
  border-left: 2px solid #e8e8e8;
}

:deep(.ant-timeline-item-head) {
  background-color: #1890ff;
  border-color: #1890ff;
}
</style>
