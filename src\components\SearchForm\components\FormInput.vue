<template>
  <a-input
    v-model:value="item.value"
    :placeholder="item.label"
    :style="item.width ? { width: `${item.width}px` } : { width: '140px' }"
    allow-clear
    v-bind="item"
    :maxlength="200"
    @keyup.enter="emtis"
  />
</template>

<script setup lang="ts">
import { FormItemType } from '../type'

const emtis = defineEmits<{
  (e: 'serach'): void
}>()

const item = defineModel<FormItemType<'input'>>('item', { required: true })
</script>

<style scoped></style>
