<template>
  <div class="gap-8px flex flex-col mb-[14px]">
    <div class="b-[1px] b-[#EAECEE] rounded-4px b-solid b-b-none relative bg-[#fafafa]">
      <div class="p-[8px]" v-show="show">
        <FormQuick :is-quicks="isQuicks" v-model:quicks="quicks" @search="emtis('search')" />
        <div class="gap-6px mb-[8px] flex flex-wrap items-center">
          <FormComponent v-model:form-arr="formArr" />
          <a-space class="search-btn-box" :size="4">
            <a-button type="primary" @click="handleQuery">
              <span>查询</span>
            </a-button>
            <a-button @click="handleReset">
              <span>重置</span>
            </a-button>
            <a-button :icon="h(SettingOutlined)" @click="emtis('setting')">自定义设置</a-button>
            <SearchFormQuick :page-type="pageType" v-model:form-arr="formArr" @search="handleQuery" />
          </a-space>
        </div>
      </div>
      <SearchFormUnfold v-model:show="show" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { h } from 'vue'
import { SettingOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import SearchFormUnfold from './components/SearchFormUnfold.vue'
import FormComponent from './components/FormComponent.vue'
import SearchFormQuick from './components/SearchFormQuick.vue'
import { FormItem } from './type'

const emtis = defineEmits<{
  (e: 'search'): void
  (e: 'setting'): void
}>()

const props = defineProps<{
  clearCb?: () => void
  pageType: number
}>()

const show = ref(true)

const isQuicks = computed(() => formArr.value.some((v) => v.isQuicks && v.isShow))
const quicks = computed(() => formArr.value.filter((v) => v.isQuicks && v.isShow))

const formArr = defineModel<FormItem[]>('form', { required: true })

const handleQuery = () => {
  // 检查批量输入是否超过200个
  for (const item of formArr.value) {
    if (item.type === 'batch-input' && item.value) {
      const itemCount = getBatchInputCount(item.value)
      if (itemCount > 200) {
        message.error(`${item.label}批量筛选最多支持200个`)
        return
      }
    }
  }

  emtis('search')
}

// 获取批量输入的项目数量
const getBatchInputCount = (value: string) => {
  if (!value) return 0

  let val = value
  val = val.replace(/\n/g, ',')
  val = val.replace(/，/g, ',')
  val = val.replace(/;/g, ',')
  val = val.replace(/；/g, ',')

  const arr = val.split(',').filter((item) => item.trim())
  return arr.length
}

const handleReset = () => {
  formArr.value.forEach((item) => {
    item.value = item.value instanceof Array ? [] : null
  })
  props?.clearCb && props.clearCb()
  handleQuery()
}

// 若没有值初始化复选筛选
const initScreening = () => {
  const path = useRoute().path
  const obj: any = localStorage.getItem('screeningObj') ? JSON.parse(localStorage.getItem('screeningObj') || '') : {}
  if (obj[path]) {
    const arr = [] as any
    formArr.value.forEach((y) => {
      obj[path].forEach((x) => {
        if (x.key === y.key) {
          y.isShow = x.isShow
          arr.push(y)
        }
      })
    })
    formArr.value = arr
  } else {
    formArr.value = formArr.value.map((v: any) => ({ ...v, isShow: v.isShow !== false }))
  }
}

onMounted(() => {
  initScreening()
})
</script>

<style scoped lang="scss"></style>
