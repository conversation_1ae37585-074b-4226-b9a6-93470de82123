import { registerSW } from 'virtual:pwa-register'

// 存储注册对象
let swRegistration: ServiceWorkerRegistration | undefined

const updateSW = registerSW({
  onNeedRefresh() {
    console.log('PWA 更新可用')
    // 这里只记录，不强制更新
  },
  onOfflineReady() {
    console.log('PWA 离线就绪')
  },
  onRegistered(registration) {
    console.log('Service Worker 已注册')
    swRegistration = registration

    // 注册后立即检查更新
    checkForUpdates()
  },
  onRegisterError(error) {
    console.error('PWA 注册失败:', error)
  },
})

// 正确的更新检查方法
const checkForUpdates = () => {
  if (swRegistration) {
    console.log('主动检查更新...')
    // 确保使用正确的上下文调用 update
    swRegistration.update().catch((err) => {
      console.error('更新检查失败:', err)
    })
  } else {
    console.warn('尝试检查更新但 Service Worker 未注册')
  }
}

// 定时检查更新
const injectUpdate = () => {
  const interval = 2 * 60 * 1000 // 每5分钟检查一次

  // 初始检查
  checkForUpdates()

  // 定时检查
  setInterval(() => {
    checkForUpdates()
  }, interval)
}

// 手动触发更新并刷新
const triggerUpdate = () => {
  if (swRegistration) {
    // 使用虚拟模块提供的更新方法
    updateSW(true)
  }
}

export { injectUpdate, triggerUpdate }
